/* 格式化时间*/
export function formatTime(val, num) {
  const date = new Date(val * 1000)
  const year = date.getFullYear()
  const month = addZero(date.getMonth() + 1)
  const day = addZero(date.getDate())
  const hour = addZero(date.getHours())
  const min = addZero(date.getMinutes())
  const sec = addZero(date.getSeconds())
  var str = ''
  if (num === 1) {
    str = year + '-' + month + '-' + day + ' ' + hour + ':' + min + ':' + sec
  } else if (num === 2) {
    str = month + '-' + day + ' ' + hour + ':' + min + ':' + sec
  } else if (num === 4) {
    str = year + '/' + month + '/' + day
  } else if (num === 5) {
    str = hour + ':' + min + ':' + sec
  } else if (num === 6) {
    str = year + '-' + month + '-' + day
  } else {
    str = year + '/' + month + '/' + day + ' ' + hour + ':' + min + ':' + sec
  }
  return str
}

/* 补0 */
export function addZero(val) {
  const str = val < 10 ? '0' + val : val
  return str
}

export function handleMins(arr) {
  var temp = []
  arr.map(item => {
    temp.push(binaryAddZero(item.toString(2)))
  })
  var str = temp.join('')
  var data = []
  for(var i in str) {
    if(str[i] === '1') {
      data.push(parseInt(i))
    }
  }
  return data
}

/* 给32位二进制补0 */
export function binaryAddZero(str) {
  var len = str.length;
  var s1 = "00000000000000000000000000000000"; //用于补齐，满32位
  if (len < 32) {
      var s2 = s1.slice(0, 32 - len); //截取需要补齐的位数
      str = s2 + str; //在前面进行补齐
  }
  return str
}