import Vue from "vue";
import VueI18n from "vue-i18n";
import Cookies from "js-cookie";
import elementEnLocale from "element-ui/lib/locale/lang/en"; // element-ui lang
import elementZhLocale from "element-ui/lib/locale/lang/zh-CN"; // element-ui lang
import elementRuLocale from "element-ui/lib/locale/lang/ru-RU";
import enLocale from "./en";
import zhLocale from "./zh";
import kyLocale from "./ky";
import ruLocale from "./ru";

Vue.use(VueI18n);

const messages = {
  en: {
    ...enLocale,
    ...elementEnLocale
  },
  zh: {
    ...zhLocale,
    ...elementZhLocale
  },
  ky: {
    ...kyLocale,
    ...elementEnLocale
  },
  ru: {
    ...ruLocale,
    ...elementRuLocale
  }
};
const navigatorLang = (navigator.language || navigator.browserLanguage).toLowerCase();
const i18n = new VueI18n({
  // 设置 locale
  // 支持的选项: en | zh | ru | ky
  locale:
    Cookies.get("language") || (navigatorLang.includes("ru") ? "ru" : navigatorLang.includes("en") ? "en": "ky"), // set locale messages
  messages
});
Cookies.set("language", i18n.locale);
export default i18n;
