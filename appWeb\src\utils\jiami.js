import CryptoJS from 'crypto-js'

var key = CryptoJS.enc.Utf8.parse("8NONwyJtHesysWpM");

//解密方法
export function decryption(word) {
  var encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  var encryptedBase64Str = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  var decryptedData = CryptoJS.AES.decrypt(encryptedBase64Str, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
  });
  var decryptedStr = decryptedData.toString(CryptoJS.enc.Utf8);
  return decryptedStr;
}
    
//加密方法
export function encryption(plaintText) {
  var encryptedData = CryptoJS.AES.encrypt(plaintText, key, {
    mode: CryptoJS.mode.ECB,
    padding: CryptoJS.pad.Pkcs7
  });
  return encryptedData.ciphertext.toString();
}

//key随机生成的解密方法
export function decryptionKey(word, keyStr, len) {
  var key = CryptoJS.enc.Utf8.parse(keyStr);
  // let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  // let encryptedBase64Str = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  let decryptedData = CryptoJS.AES.decrypt(word, key, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
  });
  // console.log(decryptedData)
  var decArray = hexStringToArray(decryptedData.toString());
  let decrypted_str = decArray.slice(decArray.length - len).join('')
  return decrypted_str;
}

//十六进制字符串转换为十进制字符串数组
function hexStringToArray(str) {
  var pos = 0;
  var len = str.length;
  if (len % 2 != 0) {
    return null;
  }
  len /= 2;
  var arrBytes = new Array();
  for (var i = 0; i < len; i++) {
    var s = str.substr(pos, 2);
    // arrBytes.push(s);
    arrBytes.push(String.fromCharCode(parseInt(s, 16)));
    pos += 2;
  }
  return arrBytes;
}