<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    top="20%"
    append-to-body
    @close="hideDialog">
    <section>
      <div class="content">
        <p class="tip">{{ tip }}</p>
      </div>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="del" >{{ $t('sure') }}</el-button>
      <el-button type="info" @click="hideDialog()">{{ $t('cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: 'DelDialog',
  components: {

  },
  props: {
    request: {
      type: Function,
      default: undefined
    },
    listQuery: {
      type: Object,
      default() {
        return {}
      }
    },
    title: {
      type: String,
      default: ''
    },
    tip: { // 提示语句
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      dialogVisible: false,
      name: '',
      loading: false
    }
  },
  methods: {
    show(val) {
      this.dialogVisible = true
      this.name = val
      console.log(this.name)
    },
    hideDialog() {
      this.dialogVisible = false
    },
    del() { // 删除
      if (!this.request || typeof this.request !== 'function') {
        throw new Error('Failed Request Function：' + JSON.stringify(this.request))
      }
      this.loading = true
      this.request(this.listQuery).then(response => {
        this.$emit('handleDel', this.name, response)
        this.loading = false
        this.hideDialog()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  /deep/ .el-dialog{
    width: 346px;
    border-radius: 4px;
  }
  .content {
    text-align: center;
    .tip {
      font-size: 14px;
      color: #575C61;
      font-weight: bold;
    }
  }

</style>
