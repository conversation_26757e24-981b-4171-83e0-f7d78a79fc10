<template>
  <div id="app">
    <router-view v-if="isRouterAlive"/>
  </div>
</template>

<script>
// import { decryption } from '@/utils/jiami'
// import Cookies from 'js-cookie'
import Vue from 'vue'
Vue.prototype.$bus = new Vue()
export default{
  name: 'App',
  provide() {
    return {
      reload: this.reload
    }
  },
  data() {
    return {
      isRouterAlive: true
    }
  },
  created() {
    // // 刷新界面websocket断开重连
    // if (sessionStorage.username && sessionStorage.username !== '') {
    //   this.$websocket.initWebSocket(JSON.parse(decryption(Cookies.get('loginData'))))
    // }
  },
  methods: {
    reload() {
      this.isRouterAlive = false
      this.$nextTick(function() {
        this.isRouterAlive = true
      })
    }
  }
}
</script>
<style lang="scss">
::-webkit-scrollbar {
width: 5px;
height: 5px;
}
::-webkit-scrollbar-track {
background-color: rgba(0, 0, 0, 0.2);
-webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:vertical {
height: 15px;
background-color: rgba(125, 125, 125, 0.7);
-webkit-border-radius: 6px;
}
::-webkit-scrollbar-thumb:horizontal {
width: 15px;
background-color: rgba(125, 125, 125, 0.7);
-webkit-border-radius: 6px;
}
</style>
