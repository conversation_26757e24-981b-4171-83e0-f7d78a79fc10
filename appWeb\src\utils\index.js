import i18n from '@/lang'
/* 格式化表格数据*/
export function handleColumnShow(data) {
  if (Object.prototype.toString.call(data) === '[object Array]') {
    if (data.length !== 0) {
      return '<p>' + data.join('</p><p>') + '</p>'
    } else {
      return i18n.t('nodata')
    }
  } else {
    return typeof data === 'number' ? data : (data || '--')
  }
}

// 文件转二进制
export function stringToByte(dataurl) {
  var arr = dataurl.split(',')
  var bstr = atob(arr[1])
  var n = bstr.length
  var bytes = new Array(n)
  while (n--) {
    bytes[n] = bstr.charCodeAt(n) & 0xFF
  }
  return bytes
}

// 图片转二进制
export function dataURLtoBlob(dataurl) {
  var arr = dataurl.split(',')
  var mime = arr[0].match(/:(.*?);/)[1]
  var bstr = atob(arr[1])
  var n = bstr.length
  var u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], {
    type: mime
  })
}