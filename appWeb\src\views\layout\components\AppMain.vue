<template>
  <section class="app-main">
    <transition name="fade-transform">
      <keep-alive>
        <router-view v-if="isKeepAlive" :key="key" />
      </keep-alive>
    </transition>
    <router-view v-if="!isKeepAlive" :key="key" />
  </section>
</template>

<script>
export default {
  name: 'AppMain',
  computed: {
    key () {
      return this.$route.fullPath
    },
    isKeepAlive () {
      let keepAliveList = ['/user', '/device', '/system', '/logs', '/video_management', '/home']
      return keepAliveList.includes(this.$route.fullPath)
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: opacity 0.25s, transform 0.25s;
  position: absolute;
  width: 100%;
}

.fade-transform-enter,
.fade-transform-leave-to {
  opacity: 0;
}
</style>
