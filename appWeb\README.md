# admin_web

> A Vue.js project

## 项目环境
使用Node 14构建。高于该node版本时需要降级node版本才能运行

## Build Setup

``` bash
# install dependencies
npm install

# serve with hot reload at localhost:8989
npm run dev

# build for production environment
npm run build

```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
