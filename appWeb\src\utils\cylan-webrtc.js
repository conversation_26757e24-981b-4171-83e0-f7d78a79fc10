// 登录连接状态
const CylanRtcStateUnkown = -1;            // 未知错误
const CylanRtcStateConnectOK = 1;          // websocket连接成功
const CylanRtcStateConnectFailed = 2;      // websocket连接失败
const CylanRtcStateLoginOK = 3;            // 登录信令服务器成功
const CylanRtcStateLoginFailed = 4;        // 登录信令服务器失败

// view端状态
const CylanRtcStateCallOK = 5;             // view呼叫成功--发起callRequest并收到callRespone
const CylanRtcStateCallFailed = 6;         // view呼叫失败，比如对端不在线或者设备超过最大连接数
const CylanRtcStateHistoryOK = 7;          // view获取历史视频列表成功--发起historyRequest并收到historyRespone
const CylanRtcStateHistoryFailed = 8;      // view获取历史视频列表失败
const CylanRtcStateForwardOK = 9;          // view透传消息成功--发起forwardRequest并收到forwardRespone
const CylanRtcStateForwardFailed = 10;     // view透传消息失败
const CylanRtcStateSwitchVideoOK = 11;     // view切换视频成功--发起switchVideo并收到switchVideo
const CylanRtcStateSwitchVideoFailed = 12; // view切换视频失败
const CylanRtcStateSwitchAudioOK = 13;     // view切换音频成功--发起switchAudio并收到switchAudio
const CylanRtcStateSwitchAudioFailed = 14; // view切换音频失败
const CylanRtcStateHangupOK = 15;          // view挂断成功--发起hangup并收到hangup
const CylanRtcStateHangupFailed = 16;      // view挂断失败

// master端状态
const CylanRtcStateRemoteCall = 100;        // master收到呼叫消息--callRequest
const CylanRtcStateRemoteHistoryCall = 101; // master收到呼叫消息--callRequest，isHistory=true
const CylanRtcStateRemoteHistory = 102;     // master收到历史视频消息--historyRequest
const CylanRtcStateRemoteForward = 103;     // master收到透传消息--forwardRequest
const CylanRtcStateRemoteSwitchVideo = 104; // master收到切换视频消息--switchVideo
const CylanRtcStateRemoteSwitchAudio = 105; // master收到切换音频消息--switchAudio
const CylanRtcStateRemoteHangup = 106;      // master收到挂断消息--hangup

// ice监听状态
const CylanRtcStateReceiveVideo = 200;         // view端收到视频流
const CylanRtcStateIceStateConnected = 201;    // view通道建立成功
const CylanRtcStateIceStateDisconnected = 202;  // view通道断开连接

const offerOptions = {
    offerToReceiveAudio: 1,
    offerToReceiveVideo: 1
};

const AuthByConn     = 0; //使用并发数
const AuthByCode     = 1; //使用许可
const AuthByApiToken = 2; //使用API token


class CylanRtcSession {
    constructor() {
        this.pc = null;
        this.peerId = null;
        this.localStream = null;
        this.offer = "";
        this.permitCode = "";
        this.gRemoteVideo;
        this.gLocalVideo;
        this.gWSConn;
        this.gUserId;
        this.gStunAddr;
        this.gTurnAddr;
        this.gTurnUser;
        this.gTurnPass;
        this.gHBTimer = null;
        this.gClearTimer = null;
        this.gReloginTimer = null;
        this.gServer;
        this.gKey;
        this.gSecret;
        this.gAuthToken = "";
        this.gAuthType = 0;
        this.isClear = false;

        this.isAnswer = false;
        this.candidates = [];

        this.gStateCB = null;
        this.gContext;

        this.gMid = 0;
        this.gMsg = "";

        this.isHangup = false
    }

    // *********************对外提供的接口
    async SetRemoteVideo(video) {
        console.log("----set video handle", video);
        this.gRemoteVideo = video;
        // this.gRemoteVideo.addEventListener('loadedmetadata', function() {
        //     console.log(`Remote video videoWidth: ${this.videoWidth}px,  videoHeight: ${this.videoHeight}px`);
        // });

        // this.gRemoteVideo.addEventListener('resize', () => {
        //     console.log(`Remote video size changed to ${this.gRemoteVideo.videoWidth}x${this.gRemoteVideo.videoHeight}`);
        // });
    }

    async Connect(server, key, secret) {
        this.gServer = server;
        this.gKey = key;
        this.gSecret = secret;
        this.gAuthType = AuthByConn;
        this.gAuthToken = "";
        this.isHangup = false

        signIn(this);
    }

    async ApiConnect(server, authToken) {
      this.gServer = server;
      this.gKey = "";
      this.gSecret = "";
      this.gAuthType = AuthByApiToken
      this.gAuthToken = authToken

      signIn(this);
  }

    async Close() {
      await this.Hangup();
      this.sendMessageToPeer("signout", {});
  }

    async Call(peerId, permitCode, isHistory) {
        if (this.gUserId === undefined) {
            console.log("please signIn first");
            this.callStateCB(CylanRtcStateLoginFailed, 0, "Please login first", "");
            return
        }

        // this.Hangup();
        this.isHangup = false
        this.peerId = peerId;
        this.permitCode = permitCode;

        this.localStream = await this.openMediaDevice(true, true);
        var pc = this.createPC();
        try {
            console.log('createOffer start');
            var offer = await pc.createOffer(offerOptions);
            console.log(`offer created. `, offer);
            await this.onCreateOfferSuccess(pc, offer);
            this.offer = offer;
            this.pc = pc;
            this.sendMessageToPeerEx("callRequest", offer, isHistory);
        } catch (e) {
            this.onCreateSessionDescriptionError(e);
        }
    }

    GetPeerConnectionState() {
        if (this.pc == null) {
            return "null"
        }
        console.log(`----------GetPeerConnectionState ${this.pc.connectionState}`)
        return this.pc.connectionState
    }

    IsPeerConnected() {
        if (this.pc == null) {
            return false
        }
        console.log(`----------GetPeerConnectionState ${this.pc.connectionState}`)
        return this.pc.connectionState == "connected"
    }

    CloseIMWebsocket() {
      console.log(this.gWSConn)
      if(this.gWSConn.url){
        this.gWSConn.close() // 关闭 websocket
        // this.gWSConn.onclose = function (e) {
        //     console.log(e)//监听关闭事件
        //     console.log('关闭Websocket')
        // }
      }
    }

    async Hangup() {
        // if (this.isAnswer == false) {
        //     return;
        // }
        this.sendMessageToPeer("hangup", {});
        await this.reset();
        this.isHangup = true
        console.log('Ending call');
    }

    async SwitchAudio(act, typ) {
        this.sendMessageToPeer("switchAudio", { "act": act, "type": typ }); // {'act':0, type':0} act=0[APP控制设备] act=1[APP控制本机] 0-关 1-开
    }

    async SwitchVideo(act, typ) {
        this.sendMessageToPeer("switchVideo", { "act": act, "type": typ });
    }

    async History(peerId, permitCode, jsonData) {
        this.peerId = peerId;
        this.permitCode = permitCode;
        // await this.reset();
        this.sendMessageToPeer("historyRequest", jsonData)
    }

    async HistorySetup(time, long) {
        this.sendMessageToPeer("historySetupRequest", {"time": time, "duration": long});
    }

    async Forward(peerId, permitCode, jsonData) {
        this.peerId = peerId;
        this.permitCode = permitCode;
        this.sendMessageToPeer("forwardRequest", jsonData);
    }

    SetLocalVideo(video) {
        this.gLocalVideo = video;
        this.gLocalVideo.addEventListener('loadedmetadata', function() {
            console.log(`Local video videoWidth: ${this.videoWidth}px,  videoHeight: ${this.videoHeight}px`);
        });
    }

    SetStateCB(cb, context) {
            this.gStateCB = cb;
            this.gContext = context;
        }
        // *********************对外提供的接口
}


CylanRtcSession.prototype.callStateCB = function(mid, sid, msg, jsonData) {
    console.log('callStateCB')
    console.log("---gMid:" + this.gMid + "mid:" + mid)
    console.log("---gMsg:" + this.gMsg + "msg:" + msg)
    if (this.gMid == mid && this.gMsg == msg) {
        // 把历史视频的消息放开
        if(mid != 7) {
            console.log("---gMid:" + this.gMid + "mid:" + mid);
            return;
        }
    }
    this.gMid = mid;
    this.gMsg = msg;
    if (this.gStateCB != null) {
        this.gStateCB(mid, sid, msg, jsonData, this.gContext);
    }
}

CylanRtcSession.prototype.reset = async function() {
    this.gMid = 0;
    this.gMsg = "";
    this.isAnswer = false;
    if (this.pc) {
        await this.pc.close();
    }
    if (this.localStream) {
        await this.localStream.getTracks().forEach(function(track) {
            track.stop();
        });
    }
}

CylanRtcSession.prototype.openMediaDevice = async function(keepAudio, keepVideo) {
    if (!keepAudio && !keepVideo) {
        return;
    }
    console.log('openMediaDevice');
    try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: keepAudio, video: keepVideo });
        console.log('---------Received local stream');
        if (keepVideo) {
            this.gLocalVideo.srcObject = stream;
        }
        return stream;
    } catch (e) {
        console.log(`----------getUserMedia() error: ${e.name}`);
    }
}

CylanRtcSession.prototype.createPC = function() {
    let localStream = this.localStream;
    if (localStream) {
        const videoTracks = localStream.getVideoTracks();
        const audioTracks = localStream.getAudioTracks();
        if (videoTracks.length > 0) {
            console.log(`Using video device: ${videoTracks[0].label}`);
        }
        if (audioTracks.length > 0) {
            console.log(`Using audio device: ${audioTracks[0].label}`);
        }
    }

    var config = {
        iceServers: [],
    };

    if (this.gStunAddr.length > 0) {
        for (let i = 0; i < this.gStunAddr.length; i++) {
            const stunServer = { urls: this.gStunAddr[i] };
            config["iceServers"].push(stunServer);
        }
    }

    if (this.gTurnAddr.length > 0) {
        for (let i = 0; i < this.gTurnAddr.length; i++) {
            const turnServer = {
                urls: this.gTurnAddr[i],
                username: this.gTurnUser[i],
                credential: this.gTurnPass[i]
            };
            config["iceServers"].push(turnServer);
        }
    }

    config["iceTransportPolicy"] = "all";
    if (this.relayOnly) {
        config["iceTransportPolicy"] = "relay";
        console.log("relay only enabled");
    }
    //console.log("config ", config);
    var pc = new RTCPeerConnection(config);
    console.log('Created local peer connection');
    pc.addEventListener('icecandidate', e => this.onIceCandidate(pc, e));
    pc.addEventListener('iceconnectionstatechange', e => onIceStateChange(this, e));
    pc.addEventListener('track', e => getRemoteStream(this, e));

    if (localStream) {
        localStream.getTracks().forEach(track => pc.addTrack(track, localStream));
        console.log('Added local stream to pc');
    }
    return pc;
}

CylanRtcSession.prototype.onCreateSessionDescriptionError = function(error) {
    console.log(`Failed to create session description: ${error.toString()}`);
}

CylanRtcSession.prototype.onCreateOfferSuccess = async function(pc, desc) {
    try {
        await pc.setLocalDescription(desc);
        console.log(`setLocalDescription complete`);
    } catch (e) {
        this.onSetSessionDescriptionError(e);
    }
}

CylanRtcSession.prototype.onSetSessionDescriptionError = function(error) {
    console.log(`Failed to set session description: ${error.toString()}`);
}

CylanRtcSession.prototype.onRemoteCandidatesIncoming = async function(cand) {
    console.log('onRemoteCandidatesIncoming ');
    await this.addIceCandidate(this.pc, cand['candidate']);
}

CylanRtcSession.prototype.onRemoteCandidatesIncoming2 = async function(cand) {
    console.log('onRemoteCandidatesIncoming2 isAnswer:', this.isAnswer);
    if (this.isAnswer == false) {
        this.candidates.push(cand);
        return;
    }
    await this.addIceCandidate(this.pc, cand);
}

CylanRtcSession.prototype.addIceCandidate = async function(pc, cand) {
    // if (this.disableHostCandidate) {
    //     if (cand.candidate && cand.candidate.search("host") >= 0) {
    //         console.log("drop remote host candidate");
    //         return;
    //     }
    // }

    try {
        // console.log("add remote IceCandidate", cand)
        await pc.addIceCandidate(cand);
    } catch (e) {
        onAddIceCandidateError(e);
    }
}

CylanRtcSession.prototype.onRemoteAnswerIncoming = async function(answer) {
    try {
        await this.pc.setRemoteDescription(answer);
         for (var i = 0; i < this.candidates.length; i++) {
             await this.onRemoteCandidatesIncoming2(this.candidates[i]);
        }
        this.candidates = [];
        console.log('onRemoteAnswerIncoming complete');
    } catch (e) {
        this.onSetSessionDescriptionError(e);
    }
}

CylanRtcSession.prototype.handleLocalCandidate = function(candidate) {
    if (candidate) {
        if (candidate.candidate.search("host") >= 0 && candidate.candidate.search("tcp") >= 0) {
            console.log("drop host tcp candidate");
            return;
        }
        // if (this.disableHostCandidate && candidate.candidate.search("host") >= 0) {
        //     console.log("drop local host candidate");
        //     return;
        // }
    }

    this.sendMessageToPeer("remoteCandidate", { 'type': 'candidate', 'candidate': candidate });
}

CylanRtcSession.prototype.onIceCandidate = async function(pc, event) {
    if (event.candidate === null) {
        console.log("ice condidate gathered done")
    } else {
        this.handleLocalCandidate(event.candidate);
    }
}

CylanRtcSession.prototype.onMessage = async function(data) {
    var jmsg = JSON.parse(data)

    // console.log("got message", jmsg)
    switch (jmsg['opcode']) {
        case "signin":
            if (jmsg['code'] != 0) {
                this.callStateCB(CylanRtcStateLoginFailed, 0, jmsg['msg'], "");
                return;
            }

            this.gUserId = jmsg['sid'];
            this.gStunAddr = jmsg['stunAddr'];
            this.gTurnAddr = jmsg['turnAddr'];
            this.gTurnUser = jmsg['turnUser'];
            this.gTurnPass = jmsg['turnPass'];
            console.log("signIn got Sid " + this.gUserId);
            this.callStateCB(CylanRtcStateLoginOK, this.gUserId, "Login success", "");
            break;
        case "message":
            var messageType = jmsg['messageType'];

            if (jmsg['code'] != 0) {                
                if (messageType == 'historyResponse') {
                  this.callStateCB(CylanRtcStateHistoryFailed, 0, jmsg['code'], "");
                } else if (messageType == 'forwardResponse') {
                  this.callStateCB(CylanRtcStateForwardFailed, 0, jmsg['code'], "");
                } else if (messageType == 'callResponse') {
                  this.callStateCB(CylanRtcStateCallFailed, 0, jmsg['code'], "");
                } else if (messageType == 'switchAudio') {
                  this.callStateCB(CylanRtcStateSwitchAudioFailed, 0, jmsg['code'], "");
                } else if (messageType == 'switchVideo') {
                  this.callStateCB(CylanRtcStateSwitchVideoFailed, 0, jmsg['code'], "");
                } else if (messageType == 'hangup') {
                  this.callStateCB(CylanRtcStateHangupFailed, 0, jmsg['code'], "");
                }

                return;
            }
            
            var msg = jmsg["message"]===''?'':JSON.parse(jmsg["message"]);
            //console.log("parse message payload: ", msg);
            if (messageType == 'callResponse') {
                console.log("incoming answer", msg)
                if (msg.type == 'exceed') {
                  this.callStateCB(CylanRtcStateCallFailed, 0, "exceed", "");
                  break;
                }
                this.isAnswer = true;
                this.callStateCB(CylanRtcStateCallOK, 0, "Call success", "");
                await this.onRemoteAnswerIncoming(msg);
            } else if (messageType == 'historyResponse') {
                console.log("incoming history", msg)       
                this.callStateCB(CylanRtcStateHistoryOK, 0, "Get history success", jmsg["message"]);
             } else if (messageType == 'forwardResponse') {
                console.log("incoming forward", msg)
                this.callStateCB(CylanRtcStateForwardOK, 0, "Get forward success", jmsg["message"]);
            } else if (messageType == 'remoteCandidate') {
                console.log("incoming candidates ")
                await this.onRemoteCandidatesIncoming2(msg);
            } else if (messageType == 'switchAudio') {
              this.callStateCB(CylanRtcStateSwitchAudioOK, 0, jmsg['code'], jmsg["message"]);
            } else if (messageType == 'switchVideo') {
              this.callStateCB(CylanRtcStateSwitchVideoOK, 0, jmsg['code'], jmsg["message"]);
            } else if (messageType == 'hangup') {
              this.callStateCB(CylanRtcStateHangupOK, 0, jmsg['code'], "");
            } else {
                this.callStateCB(CylanRtcStateUnkown, 0, "Unknow", "");
                console.log("unknow message " + msg);
                return;
            }
            break;
        case "heartbeat":
            console.log("recv heartbeat respone");
            break;
        case "error":
            this.reset();
            console.log("recv error from server");
            break;
    }
}

CylanRtcSession.prototype.sendMessageToPeer = async function(msgType, data) {
    this.sendMessageToPeerEx(msgType, data, false);
}

CylanRtcSession.prototype.sendMessageToPeerEx = async function(msgType, data, isHistory) {
    if(this.isHangup) {
        return
    }
    if (this.peerId === undefined || this.peerId == "") {
        console.log("no peerid");
        return;
    }
    var msg = {
        'opcode': "message",
        "messageType": msgType,
        //"isAlexa": true,
        "sid": this.gUserId,
        "serialCode": "",
        "toSid": 0,
        "toSerialCode": this.peerId,
        "permitCode": this.permitCode,
        "message": JSON.stringify(data)
    };

    if (isHistory) {
      msg['isHistory'] = true;
    }

    await this.gWSConn.send(JSON.stringify(msg));
    console.log("sendMessage ", msg);
}

function signIn(session) {
    console.log("signIn(session) ", session);
    var ws = new WebSocket(session.gServer);
    session.isClear = false;
    session.gWSConn = ws;

    ws.onopen = function(event) {
        session.callStateCB(CylanRtcStateConnectOK, 0, "Connect to server success", "");
        console.log("Connection open");        

        ws.send(JSON.stringify({
            'opcode': 'signin',
            'authType': session.gAuthType,
            'authCode': session.gAuthToken,
            'key': session.gKey,
            'secret': session.gSecret,
            'loginType': 0,
            'reUseSid': session.gUserId,
        }));

        if (session.gHBTimer == null) {
            session.gHBTimer = setInterval(function() {
                    console.log("------heartbeat 60 per second");
                    ws.send(JSON.stringify({ 'opcode': 'heartbeat', }));
                },
                60000);
        }

        if (session.gClearTimer == null) {
          session.gClearTimer = setInterval(function() {
                session.gMid = 0;
                session.gMsg = "";
              },
              3000);
        }

        if (session.gReloginTimer != null) {
          clearInterval(session.gReloginTimer);
            session.gReloginTimer = null;
        }
    };
    ws.onmessage = function(event) {
        session.onMessage(event.data);
    };
    ws.onclose = function(event) {        
        console.log("ws.onclose isClear = ", session.isClear);
        clearData(session);
        if (session.gReloginTimer == null) {
          console.log("setInterval relogin time isClear = ", session.isClear);
          session.callStateCB(CylanRtcStateConnectFailed, 0, "Connect to server failed", "");
          session.gReloginTimer = setInterval(function() { signIn(session) }, 10000);
        }
    };
    ws.onerror = function(event) {
        console.log("ws.onerror isClear = ", session.isClear);
        clearData(session);
        if (session.gReloginTimer == null) {
          console.log("setInterval relogin time isClear = ", session.isClear);
          session.callStateCB(CylanRtcStateConnectFailed, 0, "Connect to server failed", "");
          session.gReloginTimer = setInterval(function() { signIn(session) }, 10000);
        }
    }
}

function clearData(session) {
  if (session.isClear === true) {
    return;
  }
  session.isClear = true;

  if (session.gHBTimer != null) {
      clearInterval(session.gHBTimer);
      session.gHBTimer = null;
  }
  if (session.gClearTimer != null) {
    clearInterval(session.gClearTimer);
    session.gClearTimer = null;
  }
}

function onAddIceCandidateError(error) {
    console.log(` failed to add ICE Candidate: ${error.toString()}`);
}

function onIceStateChange(session, event) {
    if (session.pc) {
        console.log(`ICE state: ${session.pc.iceConnectionState}`);
        //console.log('ICE state change event: ', event);
        let state = session.pc.iceConnectionState;
        if (state == "connected") {
             session.callStateCB(CylanRtcStateIceStateConnected, session.gUserId, session.pc.iceConnectionState, "");
        } else if (state=="disconnected" || state=="failed" || state=="closed") {
           session.callStateCB(CylanRtcStateIceStateDisconnected, session.gUserId, session.pc.iceConnectionState, "");
        }       
    }
}

function getRemoteStream(session, e) {
    //console.log("-----get video stream", session.gRemoteVideo);
    if (session.gRemoteVideo.srcObject !== e.streams[0]) {
        session.gRemoteVideo.srcObject = e.streams[0];
        console.log('received remote stream');
        session.callStateCB(CylanRtcStateReceiveVideo, session.gUserId, "received remote stream", "");
    }
}

function bundleSdpWithCandidates(desc, cands) {
    var sdp = desc.sdp;
    var lines = sdp.split("\r\n")
    var result = [];
    for (var i = 0; i < lines.length; i++) {
        var l = lines[i];
        if (l.search("m=audio") >= 0 || l.search("m=video") >= 0) {
            result.push(l);
            for (var k = 0; k < cands.length; k++) {
                var cand_str = "a=" + cands[k];
                result.push(cand_str);
            }
            continue;
        }
        result.push(l)
    }
    var newsdp = "";
    for (var i = 0; i < result.length - 1; i++) {
        newsdp += result[i] + "\r\n";
    }
    newsdp += result[i];
    return { 'type': desc.type, 'sdp': newsdp };
}

export default CylanRtcSession