/**
 * This file demonstrates the process of starting WebRTC streaming using a KVS Signaling Channel.
 */
const viewer = [];

export async function startViewer(localView, remoteView, formValues, index, onStatsReport, getAudioEnabled, getRemoteStreamFlag, getConnectionStateFlag, onRemoteDataMessage) {
    console.log('[VIEWER] start viewer connection');
    viewer[index] = {}
    viewer[index].localView = localView;
    viewer[index].remoteView = remoteView;

    // Create KVS client 创建 KVS 客户端
    const kinesisVideoClient = new AWS.KinesisVideo({
        region: formValues.region,
        accessKeyId: formValues.accessKeyId,
        secretAccessKey: formValues.secretAccessKey,
        sessionToken: formValues.sessionToken,
        endpoint: formValues.endpoint,
        correctClockSkew: true,
    });

    // Get signaling channel ARN
    const describeSignalingChannelResponse = await kinesisVideoClient
        .describeSignalingChannel({
            ChannelName: formValues.channelName,
        })
        .promise();
    const channelARN = describeSignalingChannelResponse.ChannelInfo.ChannelARN;
    // console.log('[VIEWER] Channel ARN: ', channelARN);
    // [VIEWER] Channel ARN:  arn:aws:kinesisvideo:ap-southeast-1:175376937867:channel/jfg/1656561089108

    // Get signaling channel endpoints 获取信令通道端点
    const getSignalingChannelEndpointResponse = await kinesisVideoClient
        .getSignalingChannelEndpoint({
            ChannelARN: channelARN,
            SingleMasterChannelEndpointConfiguration: {
                Protocols: ['WSS', 'HTTPS'],
                Role: KVSWebRTC.Role.VIEWER,
            },
        })
        .promise();
    const endpointsByProtocol = getSignalingChannelEndpointResponse.ResourceEndpointList.reduce((endpoints, endpoint) => {
        endpoints[endpoint.Protocol] = endpoint.ResourceEndpoint;
        return endpoints;
    }, {});
    // console.log('[VIEWER] Endpoints: ', endpointsByProtocol);
    // endpointsByProtocol: 
    // {
    //   "HTTPS": "https://r-d2838eda.kinesisvideo.ap-southeast-1.amazonaws.com",
    //   "WSS": "wss://v-a83b3c8c.kinesisvideo.ap-southeast-1.amazonaws.com"
    // }

    // Create KVS Signaling Client 创建 KVS 信令客户端
    const kinesisVideoSignalingChannelsClient = new AWS.KinesisVideoSignalingChannels({
        region: formValues.region,
        accessKeyId: formValues.accessKeyId,
        secretAccessKey: formValues.secretAccessKey,
        sessionToken: formValues.sessionToken,
        endpoint: endpointsByProtocol.HTTPS,
        correctClockSkew: true,
    });

    // Get ICE server configuration 获取 ICE 服务器配置
    const getIceServerConfigResponse = await kinesisVideoSignalingChannelsClient
        .getIceServerConfig({
            ChannelARN: channelARN,
        })
        .promise();
    const iceServers = [];
    if (!formValues.natTraversalDisabled && !formValues.forceTURN) {
        iceServers.push({ urls: `stun:stun.kinesisvideo.${formValues.region}.amazonaws.com:443` });
    }
    if (!formValues.natTraversalDisabled) {
        getIceServerConfigResponse.IceServerList.forEach(iceServer =>
            iceServers.push({
                urls: iceServer.Uris,
                username: iceServer.Username,
                credential: iceServer.Password,
            }),
        );
    }
    // console.log('[VIEWER] ICE servers: ', iceServers);

    // Create Signaling Client 创建 WebRTC 信令客户端
    viewer[index].signalingClient = new KVSWebRTC.SignalingClient({
        channelARN,
        channelEndpoint: endpointsByProtocol.WSS,
        clientId: formValues.clientId,
        role: KVSWebRTC.Role.VIEWER,
        region: formValues.region,
        credentials: {
            accessKeyId: formValues.accessKeyId,
            secretAccessKey: formValues.secretAccessKey,
            sessionToken: formValues.sessionToken,
        },
        systemClockOffset: kinesisVideoClient.config.systemClockOffset,
    });

    const resolution = formValues.widescreen ? { width: { ideal: 1280 }, height: { ideal: 720 } } : { width: { ideal: 640 }, height: { ideal: 480 } };
    const constraints = {
        video: formValues.sendVideo ? resolution : false,
        audio: formValues.sendAudio,
    };
    const configuration = {
        iceServers,
        iceTransportPolicy: formValues.forceTURN ? 'relay' : 'all',
    };
    // 创建 RTCPeerConnection
    viewer[index].peerConnection = new RTCPeerConnection(configuration);
    if (formValues.openDataChannel) {
        viewer[index].dataChannel = viewer[index].peerConnection.createDataChannel('kvsDataChannel');
        viewer[index].peerConnection.ondatachannel = event => {
            event.channel.onmessage = onRemoteDataMessage;
        };
    }

    // 10s检测一次RTCPeerConnection的连接状态
    viewer[index].peerConnectionStateInterval = setInterval(() => {  
        getConnectionStateFlag(index, viewer[index].peerConnection.connectionState)
    }, 10000);
    // Poll for connection stats
    // viewer[index].peerConnectionStatsInterval = setInterval(() => {
    //     viewer[index].peerConnection.getStats().then(onStatsReport)
    // }, 1000);

    // 添加信令客户端事件监听器
    viewer[index].signalingClient.on('open', async () => {
        // console.log('[VIEWER] Connected to signaling service');

        // Get a stream from the webcam, add it to the peer connection, and display it in the local view.
        // If no video/audio needed, no need to request for the sources. 
        // Otherwise, the browser will throw an error saying that either video or audio has to be enabled.
        // if (formValues.sendVideo || formValues.sendAudio) {
            // console.log(constraints)
            try {
                viewer[index].localStream = await navigator.mediaDevices.getUserMedia(constraints);
                viewer[index].localStream.getTracks().forEach(track => {
                    viewer[index].peerConnection.addTrack(track, viewer[index].localStream)
                    if(track.kind === 'audio'){
                        track.enabled = false
                    }
                    // console.log(track)
                });
                localView.srcObject = viewer[index].localStream;
                getAudioEnabled(true)
            } catch (e) {
                console.error('[VIEWER] Could not find webcam');
                getAudioEnabled(false)
            }
        // }

        // Create an SDP offer to send to the master
        // console.log('[VIEWER] Creating SDP offer');
        await viewer[index].peerConnection.setLocalDescription(
            await viewer[index].peerConnection.createOffer({
                offerToReceiveAudio: true,
                offerToReceiveVideo: true,
            }),
        );

        // When trickle ICE is enabled, send the offer now and then send ICE candidates as they are generated. Otherwise wait on the ICE candidates.
        if (formValues.useTrickleICE) {
            // console.log('[VIEWER] Sending SDP offer');
            viewer[index].signalingClient.sendSdpOffer(viewer[index].peerConnection.localDescription);
        }
        // console.log('[VIEWER] Generating ICE candidates');
    });

    // When the SDP answer is received back from the master, add it to the peer connection.
    viewer[index].signalingClient.on('sdpAnswer', async answer => {
        console.log('[VIEWER] Received SDP answer');
        await viewer[index].peerConnection.setRemoteDescription(answer);
    });

    // When an ICE candidate is received from the master, add it to the peer connection.
    viewer[index].signalingClient.on('iceCandidate', candidate => {
        console.log('[VIEWER] Received ICE candidate');
        viewer[index].peerConnection.addIceCandidate(candidate);
    });

    viewer[index].signalingClient.on('close', () => {
        console.log('Disconnected from signaling channel');
        console.log(viewer);
    });

    viewer[index].signalingClient.on('error', error => {
        console.error('Signaling client error: ', error);
    });

    // Send any ICE candidates to the other peer 添加对等连接事件侦听器
    viewer[index].peerConnection.addEventListener('icecandidate', ({ candidate }) => {
        if (candidate) {
            // console.log('[VIEWER] Generated ICE candidate');

            // When trickle ICE is enabled, send the ICE candidates as they are generated.
            if (formValues.useTrickleICE) {
                // console.log('[VIEWER] Sending ICE candidate');
                viewer[index].signalingClient.sendIceCandidate(candidate);
            }
        } else {
            // No more ICE candidates will be generated
            console.log('[VIEWER] All ICE candidates have been generated');

            // When trickle ICE is disabled, send the offer now that all the ICE candidates have ben generated.
            if (!formValues.useTrickleICE) {
                // console.log('[VIEWER] Sending SDP offer');
                viewer[index].signalingClient.sendSdpOffer(viewer[index].peerConnection.localDescription);
            }
        }
    });

    // As remote tracks are received, add them to the remote view
    viewer[index].peerConnection.addEventListener('track', event => {
        console.log('[VIEWER] Received remote track');
        if (remoteView.srcObject) {
            return;
        }
        viewer[index].remoteStream = event.streams[0];
        remoteView.srcObject = viewer[index].remoteStream;
        getRemoteStreamFlag(index, true)
    });

    // 打开信令连接
    console.log('[VIEWER] Starting viewer connection');
    viewer[index].signalingClient.open();
    return viewer
}

export function stopViewer(index) {
    console.log('[VIEWER] Stopping viewer connection');
    // 离线设备没有viewer
    if(!viewer[index]){
        return
    }
    if (viewer[index].signalingClient) {
        viewer[index].signalingClient.close();
        viewer[index].signalingClient = null;
    }

    if (viewer[index].peerConnection) {
        viewer[index].peerConnection.close();
        viewer[index].peerConnection = null;
    }

    if (viewer[index].localStream) {
        viewer[index].localStream.getTracks().forEach(track => track.stop());
        viewer[index].localStream = null;
    }

    if (viewer[index].remoteStream) {
        viewer[index].remoteStream.getTracks().forEach(track => track.stop());
        viewer[index].remoteStream = null;
    }


    if (viewer[index].peerConnectionStateInterval) {
        clearInterval(viewer[index].peerConnectionStateInterval);
        viewer[index].peerConnectionStateInterval = null;
    }

    if (viewer[index].peerConnectionStatsInterval) {
        clearInterval(viewer[index].peerConnectionStatsInterval);
        viewer[index].peerConnectionStatsInterval = null;
    }

    if (viewer[index].localView) {
        viewer[index].localView.srcObject = null;
    }

    if (viewer[index].remoteView) {
        viewer[index].remoteView.srcObject = null;
    }

    if (viewer[index].dataChannel) {
        viewer[index].dataChannel = null;
    }
    console.log(viewer)
    return index
}
