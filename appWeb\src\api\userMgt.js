import request from "@/utils/request";

// 获取用户账号列表
export function getUserList(data) {
  return request({
    url: "v1/miru/account/list",
    method: "post",
    data
  });
}

// 新增用户账号
export function addUser(data) {
  return request({
    url: "v1/miru/account/add",
    method: "post",
    data
  });
}

// 编辑用户账号
export function editUser(data) {
  return request({
    url: "v1/miru/account/edit",
    method: "post",
    data
  });
}

// 删除用户账号
export function deleteUser(data) {
  return request({
    url: "v1/miru/account/delete",
    method: "post",
    data
  });
}

// 重置用户密码
export function resetPassword(data) {
  return request({
    url: "v1/miru/account/password/reset",
    method: "post",
    data
  });
}
