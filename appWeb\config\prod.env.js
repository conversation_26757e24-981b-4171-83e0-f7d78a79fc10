module.exports = {
  NODE_ENV: '"production"',
  ENV_CONFIG: '"prod"',
  BASE_API: '"/api/web/"',
  WS_SERVER: 'window.location.protocol =="https:"? ("wss://"+ window.location.hostname + (window.location.hostname.indexOf("yf")>-1? ":544/miru" : ":443/miru")) : "ws://"+ window.location.hostname +":643/miru"',
  RTC_SERVER: 'window.location.protocol =="https:"? "wss://"+ window.location.hostname + ":443" : "ws://"+ window.location.hostname +":546"'
}
