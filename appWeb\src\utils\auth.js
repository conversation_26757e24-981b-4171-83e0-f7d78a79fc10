import Cookies from 'js-cookie'

const TokenKey = 'bsAdmin-Token'
const RoleKey = 'bsAdmin-Role'

export function getToken() {
  return sessionStorage.token
}

export function setToken(token) {
  return sessionStorage.token = token
}

export function removeToken() {
  return sessionStorage.clear('token')
}

export function removeRole() {
  return Cookies.remove(RoleKey)
}

export function getRole() {
  return Cookies.get(RoleKey)
}

export function setRole(role) {
  return Cookies.set(<PERSON><PERSON><PERSON>, role)
}

export function getLanguage() {
  return Cookies.get('language')
}
