<template>
  <div class="userMgtBox">
    <div class="userMgtLayout">
      <!-- 按钮行 -->
      <div class="operateUserBtnBox">
        <span class="userPageTitle">{{ $t("userManagement") }}</span>
        <div>
          <el-button class="tipSubmitBtn" @click="addUser()">{{
            $t("addUser")
          }}</el-button>
          <el-button class="tipCancelBtn" @click="delBulkUser()">{{
            $t("bulkDelete")
          }}</el-button>
        </div>
      </div>
      <!-- 列表 -->
      <div class="userListBox">
        <el-table
          :data="user_list"
          :highlight-current-row="true"
          stripe
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" align="center" width="60" />
          <el-table-column
            type="index"
            align="center"
            label="#"
            width="60"
            class-name="infoText"
          />
          <el-table-column
            v-for="(l, index) in listTitle"
            :key="index"
            :prop="l.name"
            :label="l.title"
            :width="l.width"
            :column-key="l.name"
          >
            <template slot-scope="scope">
              <section
                :title="scope.row[scope.column.columnKey]"
                v-html="handleColumnShow(scope.row[scope.column.columnKey])"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('operate')">
            <!-- 相关操作按钮 -->
            <template slot-scope="scope">
              <section>
                <el-button
                  :title="$t('edit')"
                  type="text"
                  class="operateRowBtn"
                  @click="addEditUser(scope.row)"
                  >{{ $t("edit") }}</el-button
                >
                <el-button
                  :title="$t('delete')"
                  type="text"
                  class="operateRowBtn rowDeleteBtn"
                  @click="delUser(scope.row)"
                  >{{ $t("delete") }}</el-button
                >
                <el-button
                  :title="$t('resetPassword')"
                  type="text"
                  class="operateRowBtn rowResetPasswordBtn"
                  @click="resetPasswordUser(scope.row)"
                  >{{ $t("resetPassword") }}</el-button
                >
              </section>
            </template>
          </el-table-column>
          <template slot="empty">
            <el-empty
              :image="require('@/assets/img/empty.png')"
              :image-size="65"
            />
          </template>
        </el-table>
        <pagination
          v-show="total > 10"
          :total="total"
          :page.sync="listQuery.page"
          :limit.sync="listQuery.limit"
          class="text-right mar_top_0"
          @pagination="handlePaginationChange"
        />
      </div>
    </div>

    <!-- 页面弹出的对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :width="
        dialogTitle === $t('resetPassword') || dialogTitle === $t('deleteUser')
          ? '340px'
          : '500px'
      "
      :before-close="hideDialog"
    >
      <template
        v-if="dialogTitle === $t('addUser') || dialogTitle === $t('editUser')"
      >
        <el-form
          ref="userForm"
          :rules="userForm.rules"
          :model="userForm.data"
          label-width="23%"
          label-position="left"
        >
          <el-form-item
            :label="$t('account')"
            :prop="`${dialogTitle === $t('addUser') ? 'account' : ''}`"
          >
            <el-input
              v-if="dialogTitle === $t('addUser')"
              v-model="userForm.data.account"
              :placeholder="$t('inputLengthLimit', { limit: '6-20' })"
              maxlength="20"
              @input="onAccountInput"
            />
            <span style="font-weight: 400" v-else>{{
              userForm.data.account
            }}</span>
          </el-form-item>
          <el-form-item
            v-if="dialogTitle === $t('addUser')"
            :label="$t('password')"
            prop="password"
          >
            <el-input
              v-model="userForm.data.password"
              :placeholder="$t('inputLengthLimit', { limit: '6-18' })"
              maxlength="18"
              type="password"
              show-password
            />
          </el-form-item>
          <el-form-item :label="$t('nickName')" prop="nickName">
            <el-input
              v-model="userForm.data.nickName"
              :placeholder="$t('inputLengthLimit', { limit: 30 })"
              maxlength="30"
            />
          </el-form-item>
          <el-form-item :label="$t('mark')" prop="mark" class="formItemTop">
            <el-input
              v-model="userForm.data.mark"
              :placeholder="$t('inputLengthLimit', { limit: 100 })"
              maxlength="100"
              type="textarea"
            />
          </el-form-item>
        </el-form>
      </template>

      <template v-else>
        <div class="confirmDeleteText">
          {{
            dialogTitle === $t("resetPassword")
              ? $t("sureToReset")
              : $t("sureToDelete")
          }}
        </div>
      </template>

      <span slot="footer" class="dialog-footer">
        <el-button class="tipCancelBtn" @click="hideDialog()">{{
          $t("cancel")
        }}</el-button>
        <el-button
          :loading="isSubmitting"
          class="tipSubmitBtn"
          @click="submit()"
          >{{ $t("sure") }}</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getUserList, addUser, editUser, deleteUser, resetPassword } from '@/api/userMgt'
import { handleColumnShow } from '@/utils/index'
import Pagination from '@/components/Pagination'
export default {
  name: 'UserMgt',

  components: {
    Pagination,
  },
  data () {
    return {
      list_loading: false,
      user_list: [],
      multipleSelection: [],
      listTitle: [
        {
          title: this.$t('account'),
          name: 'account',
          width: '260px'
        },
        {
          title: this.$t('nickName'),
          name: 'nickName',
          width: '260px'
        },
        {
          title: this.$t('mark'),
          name: 'mark',
          width: '260px'
        },
      ],

      dialogVisible: false,
      dialogTitle: '提示',

      userForm: {
        data: {
          account: '',
          password: '',
          nickName: '',
          mark: ''
        },
        rules: {
          account: [
            { required: true, message: this.$t('inputLengthLimit', { limit: '6-20' }), trigger: 'blur', max: 20, min: 6 }
          ],
          password: [
            { required: true, message: this.$t('pleaseEnterCorrectPwd18'), trigger: 'blur', min: 6, max: 18 }
          ],
        }
      },

      isSubmitting: false,
      deleteUserAccount: '0',
      resetPwdUserAccount: '0',
      total: 0,
      listQuery: {
        limit: 10,
        page: 1
      },
    }
  },
  created () {
    this.getUsers()
  },
  methods: {
    handleColumnShow,
    // 获取用户列表
    getUsers () {
      this.list_loading = true
      getUserList({
        offset: (this.listQuery.page - 1) * this.listQuery.limit,
        limit: this.listQuery.limit
      }).then(res => {
        if (res.code === 200) {
          const list = res.datas.map(i => {
            return {
              account: i.account,
              nickName: i.name,
              mark: i.remark
            }
          })
          this.user_list = list
          this.total = res.count
        }
      }).catch(err => {
        console.log(err)
      }).finally(() => {
        this.list_loading = false
      })
    },
    // 添加用户
    addUser () {
      this.dialogTitle = this.$t('addUser')
      this.showDialog()
    },
    // 批量删除用户
    delBulkUser () {
      this.dialogTitle = this.$t('bulkDelete')
      if (this.multipleSelection.length <= 0) {
        this.$message.info(this.$t('bulkDelUserTip'))
      } else {
        this.showDialog()
        this.deleteUserAccount = this.multipleSelection.map(i => i.account).join(',')
      }
    },
    // 显示对话框
    showDialog () {
      this.dialogVisible = true
    },
    // 隐藏对话框
    hideDialog () {
      this.dialogVisible = false
      setTimeout(() => {
        this.clearFormData()
      }, 100);
    },
    // 清空表单
    clearFormData () {
      this.userForm.data = {
        account: '',
        password: '',
        nickName: '',
        mark: ''
      }
    },

    // 编辑用户
    addEditUser (val) {
      this.userForm.data = {
        account: val.account,
        password: '',
        nickName: val.nickName,
        mark: val.mark
      }
      if (val) {
        this.dialogTitle = this.$t('editUser')
        this.showDialog()
      }
    },

    // 删除用户
    delUser (val) {
      if (val) {
        this.dialogTitle = this.$t('deleteUser')
        this.showDialog()
        this.deleteUserAccount = val.account
      }
    },

    // 重置用户密码
    resetPasswordUser (val) {
      if (val) {
        this.dialogTitle = this.$t('resetPassword')
        this.showDialog()
        this.resetPwdUserAccount = val.account
      }
    },
    // 提交表单
    submit () {

      if (this.dialogTitle === this.$t('editUser') || this.dialogTitle === this.$t('addUser')) {
        this.$refs['userForm'].validate((valid) => {
          if (valid) {
            const fData = this.userForm.data
            this.isSubmitting = true
            const reqData = {
              account: fData.account,
              name: fData.nickName,
              remark: fData.mark,
            }
            if (this.dialogTitle === this.$t('addUser')) {
              reqData.password = this.$md5(fData.password)

              if (this.user_list.length > 4) {
                this.isSubmitting = false
                return this.$message.warning(this.$t('userLimitTip'))
              }
            }

            let runRequset = async () => {
              return new Promise(resolve => {
                if (this.dialogTitle === this.$t('editUser')) {
                  resolve(editUser(reqData))
                } else {
                  resolve(addUser(reqData))
                }
              })

            }

            runRequset(reqData)
              .then(res => {
                if (res.code === 200) {
                  this.getUsers()
                  this.hideDialog()
                  this.$message.success(this.$t(`${this.dialogTitle === this.$t('editUser') ? 'editSuccess' : 'addSuccess'}`))
                }
              }).catch(err => {
                console.log(err)
              }).finally(() => {
                this.isSubmitting = false
              })
          } else {
            return
          }
        })
      } else {
        this.isSubmitting = true
        if (this.dialogTitle === this.$t('deleteUser') || this.dialogTitle === this.$t('bulkDelete')) {
          const reqData = {
            account: this.deleteUserAccount
          }
          deleteUser(reqData)
            .then(res => {
              if (res.code === 200) {
                this.getUsers()
                this.hideDialog()
                this.$message.success(this.$t('deleteSuccess'))
              }
            }).finally(() => {
              this.isSubmitting = false
              this.multipleSelection = []
            })
        } else {
          const reqData = {
            account: this.resetPwdUserAccount,
          }
          resetPassword(reqData)
            .then(res => {
              if (res.code === 200) {
                this.hideDialog()
                this.$message.success(this.$t('modifySuccess'))
              }
            }).finally(() => {
              this.isSubmitting = false
            })
        }
      }



    },
    // 当输出账号输入框内容时，只允许输入字母和数字
    onAccountInput (val) {
      const filteredValue = val.replace(/[^A-Z0-9]/gi, '');
      this.userForm.data.account = filteredValue;
    },

    // 多选
    handleSelectionChange (val) {
      this.multipleSelection = val
      console.log(this.multipleSelection)
    },

    // 分页变动
    handlePaginationChange (val) {
      this.listQuery = {
        page: val.page,
        limit: val.limit
      }
      this.getUsers()
    },
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
div,
text {
  color: var(--color-neutral-100);
  font-size: var(--font-size-normal);
}
.userMgtBox {
  height: calc(100vh - 75px);
  background: var(--color-neutral-700);
}
.userMgtLayout {
  max-width: 1260px;
  margin: auto;
  box-sizing: border-box;
  padding: 24px 20px 24px;
}
.userPageTitle {
  font-size: var(--font-size-large);
  font-weight: bolder;
}
.operateUserBtnBox {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  align-items: center;
}
.userListBox {
  border-radius: 6px;
  background: var(--color-neutral-600);
  .operateRowBtn {
    padding: 0;
    color: var(--color-primary);
  }
  .rowDeleteBtn {
    color: var(--color-negative-400);
  }
  .rowResetPasswordBtn {
    color: var(--color-warning-400);
  }
}
.confirmDeleteText {
  text-align: center;
  margin-bottom: 24px;
}
</style>

