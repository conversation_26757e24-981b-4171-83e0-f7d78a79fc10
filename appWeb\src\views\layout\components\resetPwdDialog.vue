<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    append-to-body
    @close="hideDialog">
    <section>
      <el-form ref="changePwdDialogForm" :rules="changePwdDialogForm.rules" :model="changePwdDialogForm.data" label-width="100px" label-position="left">
        <el-form-item :label="$t('currentPwd')" prop="nowPassword">
          <el-input v-model="changePwdDialogForm.data.nowPassword" :placeholder="$t('pwdPlaceholder')" maxlength="18" type="password" />
        </el-form-item>
        <el-form-item :label="$t('newPwd')" prop="new_password">
          <el-input v-model="changePwdDialogForm.data.new_password" :placeholder="$t('pwdPlaceholder')" maxlength="18" type="password" />
        </el-form-item>
        <el-form-item :label="$t('confirmPwd')" prop="confirm_password">
          <el-input v-model="changePwdDialogForm.data.confirm_password" :placeholder="$t('pwdPlaceholder')" maxlength="18" type="password" />
        </el-form-item>
      </el-form>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button :loading="loading" type="primary" @click="submit()">{{ $t('confirm') }}</el-button>
      <el-button @click="hideDialog()">{{ $t('cancel') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { validPwd } from '@/utils/validate'
import { changePassword } from '@/api/login'
import { removeToken, removeRole } from '@/utils/auth'

export default {
  name: 'ChangePwdDialog',
  components: {
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    const validatePassword = (rule, value, callback) => {
      if (!validPwd(value.trim())) {
        callback(new Error(this.$t('pleaseEnterCorrectPwd')))
      } else {
        callback()
      }
    }
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.changePwdDialogForm.data.new_password) {
        callback(new Error(this.$t('noMatchNewPwd')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      changePwdDialogForm: {
        rules: {
          nowPassword: [
            { required: true, validator: validatePassword, trigger: 'blur' }
          ],
          new_password: [
            { required: true, validator: validatePassword, trigger: 'blur' }
          ],
          confirm_password: [
            { required: true, validator: validateConfirmPassword, trigger: 'blur' }
          ]
        },
        data: {
          nowPassword: '',
          new_password: '',
          confirm_password: ''
        }
      },
      loading: false,
      timestamp: Date.parse(new Date()) / 1000,
      account: sessionStorage.username,
      token: this.$store.getters.token
    }
  },
  methods: {
    show() {
      this.dialogVisible = true
      this.changePwdDialogForm.data.nowPassword = ''
      this.changePwdDialogForm.data.new_password = ''
      this.changePwdDialogForm.data.confirm_password = ''
      if (this.$refs && this.$refs.changePwdDialogForm) {
        this.$refs.changePwdDialogForm.clearValidate()
      }
    },
    hideDialog() {
      this.dialogVisible = false
    },
    submit() {
      const This = this
      This.$refs['changePwdDialogForm'].validate((valid) => {
        if (valid) { // 验证通过
          This.loading = true
          const data = {
            'time': Date.parse(new Date()) / 1000,
            'account': This.account,
            'auth_token': This.token,
            'password_old': This.$md5(This.changePwdDialogForm.data.nowPassword),
            'password_new': This.$md5(This.changePwdDialogForm.data.new_password)
          }
          changePassword(data).then(response => {
            This.hideDialog()
            This.$notify({
              dangerouslyUseHTMLString: true,
              message: '<div class="success">' + this.$t('modifySuccess') + '</div>',
              duration: 3000,
              showClose: false,
              offset: -16
            })
            This.$store.commit('SET_TOKEN', '')
            removeToken()
            removeRole()
            This.$router.push({ path: '/login' })
          })
        } else {
          console.log('Error!')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog{
    width: 475px;
    border-radius: 4px;
  }
</style>

