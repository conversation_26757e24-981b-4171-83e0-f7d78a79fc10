/* 手机号*/
export function isvalidUsername(str) {
  if (!(/^\d{7,11}$/g.test(str))) {
    return false
  } else {
    return true
  }
}

/* 只能输入英文，数字 */
export function validAZNumber(str) {
  if (!(/^[A-Za-z0-9]+$/.test(str))) {
    return false
  } else {
    return true
  }
}

/* 限制输入数字、大小写字母、特殊字符（包括“,”或“.”、“;”、、“:”、“?”、“!”、“'”、“@”） */
export function validPwdKo(str) {
  if (!(/^[a-zA-Z0-9,.;:'?!@]+$/.test(str))) {
    return false
  } else {
    return true
  }
}

/* 密码*/
export function validPwd(str) {
  if (!(/^.{6,18}$/g.test(str))) {
    return false
  } else {
    return true
  }
}

/* 密码*/
export function validPwdCylan(str) {
  if (!(/^.{6,128}$/g.test(str))) {
    return false
  } else {
    return true
  }
}

/* 16位SN号 */
export function validSN(str) {
  if (!(/^\d{16}$/g.test(str))) {
    return false
  } else {
    return true
  }
}

/* 金钱 */
export function validMoney(str) {
  var moneyRegex = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/
  return moneyRegex.test(str)
}

/* 合法uri*/
export function validateURL(textval) {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlregex.test(textval)
}

/* validate email */
export function validateEmail(email) {
  const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
  return re.test(email)
}
