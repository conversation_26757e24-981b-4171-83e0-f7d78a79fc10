<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @close="hideDialog">
    <section>
      <el-form ref="dialogForm" :rules="dialogForm.rules" :model="dialogForm.data" label-width="23%" label-position="left">
        <el-form-item v-if="!is_edit" :label="$t('sn')" prop="sn">
          <el-input v-model="dialogForm.data.sn" :placeholder="$t('snEnter')" :disabled="is_edit" maxlength="16" />
        </el-form-item>
        <el-form-item v-if="!is_edit" :label="$t('deviceVerificationCode')" prop="code">
          <el-input v-model="dialogForm.data.code" :placeholder="$t('deviceCodeEnter')" maxlength="6"/>
        </el-form-item>
        <el-form-item :label="$t('deviceNickname')" prop="name">
          <el-input v-model="dialogForm.data.name" :placeholder="$t('deviceNameEnter')" maxlength="20"/>
        </el-form-item>
      </el-form>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button class="tipCancelBtn" type="info" @click="hideDialog()">{{ $t('cancel') }}</el-button>
      <el-button class="tipSubmitBtn" :loading="loading" type="primary" @click="submit()">{{ $t('sure') }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { validSN } from '@/utils/validate'
import { editDeviceName } from '@/api/deviceMgt'
export default {
  name: 'DeviceAddDialog',
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  data() {
    const validateSn = (rule, value, callback) => {
      if (value.trim().length !== 16) {
        callback(new Error(this.$t('snEnterTip')))
      } else if (!validSN(value.trim())) {
        callback(new Error(this.$t('snEnterTip')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: false,
      dialogForm: {
        rules: {
          sn: [
            { required: true, validator: validateSn, trigger: 'blur' }
          ],
          code: [
            { required: true, message: this.$t('cannotEmpty'), trigger: 'blur' }
          ],
          name: [
            { required: true, message: this.$t('cannotEmpty'), trigger: 'blur' }
          ]
        },
        data: {
          sn: '',
          code: '',
          name: ''
        }
      },
      group_id: -10,
      is_edit: false,
      loading: false
    }
  },
  methods: {
    show(group_id, val) {
      console.log(group_id)
      this.dialogVisible = true
      if (val) {
        this.is_edit = true
        this.dialogForm.data.sn = val.sn
        this.dialogForm.data.name = val.alias
      } else {
        this.is_edit = false
      }
      this.group_id = group_id
      if (this.$refs && this.$refs.dialogForm) {
        this.$refs.dialogForm.clearValidate()
      }
    },
    hideDialog() {
      this.dialogVisible = false
      this.dialogForm.data.sn = ''
      this.dialogForm.data.code = ''
      this.dialogForm.data.name = ''
    },
    submit() {
      const _this = this
      _this.$refs['dialogForm'].validate((valid) => {
        if (valid) { // 验证通过
          const reqData = {
            cid: _this.dialogForm.data.sn,
            name: _this.dialogForm.data.name
          }
          editDeviceName(reqData)
            .then(res => {
              console.log(res)
              if (res.code === 200) {
                _this.$message.success(_this.$t('editSuccess'))
                _this.hideDialog()
                _this.$parent.getDeviceList(_this.group_id, 'refresh')
              } else {
              }
            }).catch(err => {
              console.log(err)
            }).finally(() => {
              _this.loading = false
            })
          return
          _this.loading = true
          let post_data = {}
          if (_this.is_edit) {
            post_data = {
              'group_id': -1,
              'sn': _this.dialogForm.data.sn,
              'alias': _this.dialogForm.data.name
            }
            _this.$websocket.webSocketSend('cli_dev_update', post_data)
            _this.$bus.$once('cli_dev_update_rsp', response => {
              console.log(response)
              _this.$message.success(_this.$t('editSuccess'))
              _this.loading = false
              _this.hideDialog()
              _this.$parent.getDeviceList(_this.group_id, 'refresh')
            })
          } else {
            post_data = {
              'group_id': _this.group_id > -1 ? _this.group_id : 0,
              'sn': [_this.dialogForm.data.sn],
              'key': [_this.dialogForm.data.code],
              'alias': [_this.dialogForm.data.name]
            }
            _this.$websocket.webSocketSend('cli_dev_add_muti', post_data)
            _this.$bus.$once('cli_dev_add_muti_rsp', response => {
              console.log(response)
              if (typeof (response) === 'number') {
                _this.loading = false
                _this.hideDialog()
                return
              }
              if (response.body.ret[0] !== 0) {
                _this.loading = false
                _this.hideDialog()
                const err_code = 'errCode' + response.body.ret[0]
                _this.$message.error(this.$t(err_code))
                return
              }
              _this.$message.success(_this.$t('addSuccess'))
              _this.loading = false
              _this.hideDialog()
              _this.$parent.getDeviceList(_this.group_id, 'refresh')
            })
          }
        } else {
          console.log('Error!')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
  /deep/ .el-dialog{
    width: 488px;
    border-radius: 4px;
  }
</style>

