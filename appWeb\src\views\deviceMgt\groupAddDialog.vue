<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    @close="hideDialog"
    width="700px"
  >
    <section>
      <el-form
        ref="dialogForm"
        :rules="dialogForm.rules"
        :model="dialogForm.data"
        label-width="240px"
        label-position="left"
      >
        <el-form-item :label="$t('groupName')" prop="group_name">
          <el-input
            v-model="dialogForm.data.group_name"
          />
        </el-form-item>

        <el-form-item :label="$t('groupNumber')">
          <span style="font-weight: 400">{{ number }}</span>
        </el-form-item>

        <el-form-item v-if="accountType == '1'" :label="$t('authorizedUser')" class="formItemTop">
          <div>
            <el-checkbox-group v-model="authorizedUserList">
              <el-checkbox
                v-for="(item, index) in userList"
                :key="index"
                :label="item.name ? item.name : item.account"
              ></el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
      </el-form>
    </section>
    <span slot="footer" class="dialog-footer">
      <el-button type="info" class="tipCancelBtn" @click="hideDialog()">{{
        $t("cancel")
      }}</el-button>
      <el-button :loading="loading" type="primary" class="tipSubmitBtn" @click="submit()">{{
        $t("sure")
      }}</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { editDeviceGroup, getDeviceGroupDetail } from '@/api/deviceMgt'
import { getUserList } from '@/api/userMgt'
export default {
  name: 'GroupAddDialog',
  props: {
    title: {
      type: String,
      default: ''
    },
    number: {
      type: String,
      default: ''
    }
  },
  data () {
    return {
      dialogVisible: false,
      dialogForm: {
        rules: {
          group_name: [
            { required: true, message: this.$t('cannotEmpty'), trigger: 'blur' }
          ]
        },
        data: {
          group_name: ''
        }
      },
      group_id: -10,
      is_edit: false,
      loading: false,

      authorizedUserList: [],
      userList: [],


    }
  },
  computed: {
    authorizedUserAccountList () {
      return this.userList.filter(item => this.authorizedUserList.includes(item.name ? item.name : item.account))
      .map(item => item.account)
    },
    accountType() {
      return sessionStorage.account_type
    }
  },
  created () {

  },
  methods: {
    show (val) {
      this.dialogVisible = true
      if (val) {
        this.is_edit = true
        this.dialogForm.data.group_name = val.label
        this.group_id = val.value

        if (sessionStorage.account_type == '1') {
          getUserList().then(res => {
          if (res.code === 200) {
            const list = res.datas.map(i => {
              return {
                account: i.account,
                name: i.name,
                mark: i.remark
              }
            })
            this.userList = list

            getDeviceGroupDetail({
              group_id: val.value
            }).then(res => {
              if (res.code === 200) {
                const bindAccountList = res.data.bind_account
                this.authorizedUserList = list.filter(item => bindAccountList.includes(item.account)).map(item => item.name ? item.name : item.account)
              }
            }).catch(err => {
              console.log(err)
            })
          }
        }).catch(err => {
          console.log(err)
        }).finally(() => {
          this.list_loading = false
        })
        }


      } else {
        this.is_edit = false
      }
      if (this.$refs && this.$refs.dialogForm) {
        this.$refs.dialogForm.clearValidate()
      }
    },
    hideDialog () {
      this.dialogVisible = false
      this.dialogForm.data.group_name = ''
      this.authorizedUserList = []
    },
    submit () {
      const _this = this
      _this.$refs['dialogForm'].validate((valid) => {
        if (valid) { // 验证通过
          _this.loading = true
          const reqData = {
            group_id: this.group_id,
            name: this.dialogForm.data.group_name,
            bind_account: this.authorizedUserAccountList
          }
          console.log(reqData)
          editDeviceGroup(reqData)
            .then(res => {
              console.log(res)
              if (res.code === 200) {
                _this.$message.success(_this.$t('editSuccess'))
                _this.hideDialog()
                _this.$parent.getGroup()
              } else {
              }
            }).catch(err => {
              console.log(err)
            }).finally(() => {
              _this.loading = false

            })

          return
          _this.loading = true
          let post_data = {}
          if (_this.is_edit) {
            post_data = {
              'group_id': _this.group_id,
              'alias': _this.dialogForm.data.group_name
            }
            _this.$websocket.webSocketSend('cli_dev_group_edit', post_data)
            _this.$bus.$once('cli_dev_group_edit_rsp', response => {
              _this.$message.success(_this.$t('editSuccess'))
              _this.loading = false
              _this.hideDialog()
              _this.$parent.getGroup()
            })
          } else {
            post_data = {
              'alias': _this.dialogForm.data.group_name
            }
            _this.$websocket.webSocketSend('cli_dev_group_add', post_data)
            _this.$bus.$once('cli_dev_group_add_rsp', response => {
              _this.$message.success(_this.$t('addSuccess'))
              _this.loading = false
              _this.hideDialog()
              _this.$parent.getGroup()
            })
          }
        } else {
          console.log('Error!')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
/deep/ .el-dialog {
  width: 488px;
  border-radius: 4px;
}
</style>

