<template>
  <div class="timelineBox" :class="{'opacityHalf': timelineDisabled}">
    <div class="timelineLeft" @click="moveLeft()"><img class="deviceIcon" src="../../assets/img/leftArrow.png"></div>
    <div class="timelineCenterWrap">
      <div class="showDateBox"></div>
      <canvas ref="timeline" width="" height="80" :style="{transform:'translateX(-'+ leftX + 'px)'}"></canvas>
    </div>
    <div class="timelineRight" @click="moveRight()"><img class="deviceIcon" src="../../assets/img/rightArrow.png"></div>
  </div>
</template>

<script>
export default {
  name: 'Timeline',
  props: {
    listQuery: {
      type: Object,
      default() {
        return {}
      }
    },
    showDateRectWith: { // 显示被选中的时间的方框宽度
      type: Number,
      default: 150
    },
    day: {
      type: String,
      default: ''
    },
    timelineWidth: {
      type: Number,
      default: 100
    },
  },
  data() {
    return {
      time_list: ['480', '577', '578', '579', '580', '582', '613', '615', '616', '617', '618', '628', '629', '630', '633', '634', '637', '639', '654', '687', '688'],
      timeScaleChartOptions: {
        backgroundColor: "#000000", //背景颜色
        backgroundColor1: "#191919", //背景颜色
        labelColors: ["#A4AEB9"], //字颜色
        labelBgColors: ["#159BCE"], //选中显示区域背景颜色
        selectedlabelColors: ["#F4F9FD"], //选中显示区域字颜色
        lineColors: ["#464646", "#464646", "#09BBFF", "#f56c6c", "#09bbff33"], //线颜色：横线颜色；竖线颜色; 选中竖线颜色；分钟竖线颜色；有视频的竖线颜色
        chooseIndex: 8, //默认选中
        hours: 8, //默认选中
        mins: 0, //默认选中
        clientX: 0, //默认选中
        textClientX: 0, //默认选中
        clearHover: true, // 默认没有鼠标hover的时间框和竖线等样式
        callback: this.timeScaleChartFun //回调函数，当鼠标点击和滑动使得其值更改时，会执行此函数，并把更改后的值返回给this
      },
      canvas: null,
      ctx: null,
      inited: false,
      perStep: 0,
      time_data: [],
      time_line_data: [],
      video_time_line: {},
      leftX: 0,
      timelineDisabled: false,
    }
  },
  mounted() {
    var canvas = this.$refs.timeline;
    console.log(canvas)
    canvas.width = this.timelineWidth;
    canvas.style.width = canvas.width + "px";
    canvas.style.height = canvas.height + "px";
    canvas.width = canvas.width;
    canvas.height = canvas.height;
    this.canvas = canvas
    this.TimeScaleChart();
    // this.eventListener()
    this.init();
  },
  methods: {
    timeScaleChartFun() {
      // console.log(this.timeScaleChartOptions.hours + ":" + this.timeScaleChartOptions.mins)
    },
    TimeScaleChart() {
      this.ctx = this.canvas.getContext("2d");
      this.inited = false;
      //生成x轴刻度点数组
      this.scaleXpointArr = new Array();
      this.perStep = this.canvas.width / 24;
      console.log(this.perStep)
      this.timeScaleChartOptions.clientX = this.timeScaleChartOptions.hours * this.perStep;
      this.timeScaleChartOptions.textClientX = this.handleClientX(this.timeScaleChartOptions.clientX)
      for (var i = 0; i < 24; i++) {
        this.scaleXpointArr.push((i + 1) * this.perStep);
      }
    },
    eventListener() {
      //注册mouseenter事件，用于监测鼠标进入事件
      var _this = this;
      this.canvas.addEventListener('mouseenter', function(eve) {
        _this.timeScaleChartOptions.clearHover = false
        _this.recordTime(eve)
      });
      
      //注册mousemove事件，用于监测鼠标在元素上移动事件
      this.canvas.addEventListener('mousemove', function(eve) {
        _this.timeScaleChartOptions.clearHover = false
        _this.recordTime(eve)
      });

      //注册click事件，用于监测鼠标在元素上点击事件
      this.canvas.addEventListener('click', function(eve) {
        _this.timeScaleChartOptions.clearHover = true
        var selectX = eve.offsetX <= 0? 0:eve.offsetX;
        var min_px = _this.perStep/60
        var time = 0
        for(var i = 0; i < _this.time_data.length; i++) {
          if(selectX < min_px * _this.time_data[i]) {
            time = _this.time_data[i]
            _this.video_time_line.clientX = min_px * _this.time_data[i]
            _this.video_time_line.textClientX = _this.handleClientX(_this.video_time_line.clientX)
            _this.video_time_line.hours = parseInt(_this.time_data[i] / 60)
            _this.video_time_line.mins = parseInt(_this.time_data[i] - _this.video_time_line.hours * 60)
            break;
          } else {
            time = 2000
            _this.video_time_line.clientX = 2000
          }
        }
        _this.sendTime(time)
        _this.recordTime(eve, "selected")
      });

      //注册mouseleave事件，用于监测鼠标离开事件
      this.canvas.addEventListener('mouseleave', function(eve) {
        _this.timeScaleChartOptions.clearHover = true
        _this.recordTime(eve, "clearHover")
      });
    },
    recordTime(eve) {
      var _this = this;
      var selectX = eve.offsetX <= 0? 0:eve.offsetX;
      var hours = parseInt(selectX / _this.perStep)
      var mins = parseInt((selectX - hours * _this.perStep) / _this.perStep * 60)
      _this.timeScaleChartOptions.clientX = selectX;
      _this.timeScaleChartOptions.textClientX = _this.handleClientX(_this.timeScaleChartOptions.clientX)
      _this.timeScaleChartOptions.hours = hours;
      _this.timeScaleChartOptions.mins = mins;
      _this.timeScaleChartOptions.chooseIndex = hours;
      _this.update();
      _this.timeScaleChartOptions.callback.call();
    },
    init() {
      var _this = this;
      _this.draw();
      _this.inited = true;
    },
    update(timeData, timelineDisabled, videoTime) {
      if(timeData && timeData.length > 0){
        this.time_data = timeData
        var min_px = this.perStep/60
        this.video_time_line.clientX = min_px * this.time_data[0]
        this.video_time_line.textClientX = this.handleClientX(this.video_time_line.clientX)
        this.video_time_line.hours = parseInt(this.time_data[0] / 60)
        this.video_time_line.mins = parseInt(this.time_data[0] - this.video_time_line.hours * 60)
        this.sendTime(this.time_data[0])
      }
      if(typeof(timelineDisabled) === 'boolean') {
        this.timelineDisabled = timelineDisabled
      }
      if(videoTime) {
        let min_px = this.perStep/60
        let hours = new Date(videoTime).getHours()
        let mins = new Date(videoTime).getMinutes()
        this.video_time_line.clientX = min_px * (hours *60 + mins)
        //时间轴 播放视频的时间 自动定位的时候 要把时间显示在控件中间
        if(this.video_time_line.clientX < 200) {
          this.leftX = 0
        } else if(this.video_time_line.clientX >= 200 && this.video_time_line.clientX <= 760) {
          this.leftX = min_px * (hours *60 + mins) - 200
        } else if(this.video_time_line.clientX > 760) {
          this.leftX = 560
        }
        this.video_time_line.textClientX = this.handleClientX(this.video_time_line.clientX)
        this.video_time_line.hours = hours
        this.video_time_line.mins = mins
      }
      if (this.inited) {
        this.init();
      }
    },
    draw() {
      //横线距底部偏移量
      var offsetY = 10;
      //刻度偏移量
      var scaleY = 12;

      this.ctx.save();
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.fillStyle = this.timeScaleChartOptions.backgroundColor;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.fillStyle = this.timeScaleChartOptions.backgroundColor1;
      this.ctx.fillRect(0, 0, this.canvas.width, 30);
      if(this.timelineDisabled) {
        this.ctx.globalAlpha = 0.5;
      }
      //横线
      this.ctx.beginPath();
      this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[0];
      this.ctx.lineWidth = 1;
      this.ctx.moveTo(0, this.canvas.height - offsetY);
      this.ctx.lineTo(this.canvas.width, this.canvas.height - offsetY)
      this.ctx.stroke();

      //横线 仿边框
      this.ctx.beginPath();
      this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[0];
      this.ctx.lineWidth = 1;
      this.ctx.moveTo(0, this.canvas.height - offsetY * 5);
      this.ctx.lineTo(this.canvas.width, this.canvas.height - offsetY * 5)
      this.ctx.stroke();

      for (var i = 0; i < this.scaleXpointArr.length; i++) {
        //刻度
        this.ctx.beginPath();
        this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[1];
        this.ctx.lineWidth = 1;
        //整数刻度
        this.ctx.moveTo(this.scaleXpointArr[i], this.canvas.height - offsetY - scaleY);
        this.ctx.lineTo(this.scaleXpointArr[i], this.canvas.height - offsetY)
        this.ctx.stroke();
        //文字
        this.ctx.fillStyle = this.timeScaleChartOptions.labelColors[0];
        // this.ctx.font = this.timeScaleChartOptions.fontSize + this.timeScaleChartOptions.fontName;
        var fontSize1 = 12;
        //字向下偏移量
        var font_offsetY1 = fontSize1 + 1 - offsetY * 2 - scaleY;
        this.ctx.font = fontSize1 + "px Helvetica";
        var text1 = i + ":00";
        var textWidth1 = this.ctx.measureText(text1).width;
        var textHeight1 = this.ctx.measureText(text1).height;
        //this.ctx.fillText(text1, this.scaleXpointArr[i] - textWidth1, (this.canvas.height - offsetY + font_offsetY1));
        if(i == 0) {
          this.ctx.fillText(text1, 0-textWidth1/2, (this.canvas.height - offsetY + font_offsetY1));
        } else {
          this.ctx.fillText(text1, this.scaleXpointArr[i-1] - textWidth1 / 2, (this.canvas.height - offsetY + font_offsetY1));
        }

        //分钟刻度
        // for(var j = 0; j < 60; j++) {
        //   var minScaleY = 10;
        //   this.ctx.beginPath();
        //   this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[3];
        //   this.ctx.lineWidth = 0.5;
        //   if(i == 0) {
        //     this.ctx.moveTo(this.perStep/60*j, this.canvas.height - offsetY - scaleY);
        //     this.ctx.lineTo(this.perStep/60*j, this.canvas.height - offsetY - minScaleY)
        //     this.ctx.stroke();
        //   } else {
        //     this.ctx.moveTo(this.scaleXpointArr[i-1] + this.perStep/60*j, this.canvas.height - offsetY - scaleY);
        //     this.ctx.lineTo(this.scaleXpointArr[i-1] + this.perStep/60*j, this.canvas.height - offsetY - minScaleY)
        //     this.ctx.stroke();
        //   }
        // }
      }
      // 有视频的时间线
      if(this.time_data && this.time_data.length > 0){
        var min_px = this.perStep/60
        for(var t = 0; t < this.time_data.length; t++) {
          this.ctx.beginPath();
          this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[4];
          this.ctx.lineWidth = 1;
          this.ctx.moveTo(min_px * this.time_data[t], offsetY * 3);
          this.ctx.lineTo(min_px * this.time_data[t], this.canvas.height)
          this.ctx.stroke();
        }
      }
      // 选中播放视频的时间 显示竖线、时间、时间框
      if(typeof(this.video_time_line.clientX) === "number" && this.video_time_line.clientX < 2000){
        //选中文字的框
        this.drawRoundRectPath(this.ctx, this.video_time_line.textClientX - this.showDateRectWith/2, 3, this.showDateRectWith, 23, 11)
        //选中刻度
        this.ctx.beginPath();
        this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[2];
        this.ctx.lineWidth = 1;
        this.ctx.moveTo(this.video_time_line.clientX, offsetY * 3);
        this.ctx.lineTo(this.video_time_line.clientX, this.canvas.height)
        this.ctx.stroke();
        //选中文字
        this.ctx.fillStyle = this.timeScaleChartOptions.selectedlabelColors[0];
        var fontSize2 = 16;
        var font_offsetY2 = fontSize2/2 - scaleY * 4 - offsetY * 2;
        this.ctx.font = fontSize2 + "px Helvetica";
        var text2 = this.day + ' ' + this.addZero(this.video_time_line.hours) + ":" + this.addZero(this.video_time_line.mins);
        var textWidth2 = this.ctx.measureText(text2).width;
        this.ctx.fillText(text2, this.video_time_line.textClientX - textWidth2/2, this.canvas.height + font_offsetY2);
      }
      // 鼠标进入悬停移动时显示的时间
      if(!this.timeScaleChartOptions.clearHover){        
        //文字的框
        this.drawRoundRectPath(this.ctx, this.timeScaleChartOptions.textClientX - this.showDateRectWith/2, 3, this.showDateRectWith, 23, 11)
        //刻度
        this.ctx.beginPath();
        this.ctx.strokeStyle = this.timeScaleChartOptions.lineColors[2];
        this.ctx.lineWidth = 1;
        this.ctx.moveTo(this.timeScaleChartOptions.clientX, offsetY * 3);
        this.ctx.lineTo(this.timeScaleChartOptions.clientX, this.canvas.height)
        this.ctx.stroke();
        //文字
        this.ctx.fillStyle = this.timeScaleChartOptions.selectedlabelColors[0];
        var fontSize3 = 16;
        var font_offsetY3 = fontSize3/2 - scaleY * 4 - offsetY * 2;
        this.ctx.font = fontSize3 + "px Helvetica";
        var text3 = this.day + ' ' + this.addZero(this.timeScaleChartOptions.hours) + ":" + this.addZero(this.timeScaleChartOptions.mins);
        var textWidth3 = this.ctx.measureText(text3).width;
        this.ctx.fillText(text3, this.timeScaleChartOptions.textClientX - textWidth3/2, this.canvas.height + font_offsetY3);
      }
    },
    // 圆角矩形
    drawRoundRectPath(ctx, x, y, width, height, radius) {
      ctx.beginPath();
      ctx.arc(x + radius, y + radius, radius, Math.PI, Math.PI * 3 / 2);    
      ctx.lineTo(width - radius + x, y);    
      ctx.arc(width - radius + x, radius + y, radius, Math.PI * 3 / 2, Math.PI * 2);    
      ctx.lineTo(width + x, height + y - radius);    
      ctx.arc(width - radius + x, height - radius + y, radius, 0, Math.PI * 1 / 2);    
      ctx.lineTo(radius + x, height +y);    
      ctx.arc(radius + x, height - radius + y, radius, Math.PI * 1 / 2, Math.PI);    
      ctx.fillStyle = this.timeScaleChartOptions.labelBgColors[0]; //为圆角矩形填充颜色
      // ctx.strokeStyle = this.timeScaleChartOptions.labelBgColors[0]; //矩形边框颜色
      ctx.closePath(); //闭合绘制的路径
      ctx.fill(); //填充当前的路径,默认颜色是黑色
      ctx.stroke(); //绘制确切的路径
    },
    addZero(val) {
      const str = val < 10 ? '0' + val : val
      return str
    },
    moveLeft() {
      if(this.leftX >= 560){
        this.leftX = 560
        return
      }
      this.leftX += this.perStep
      if(this.timeScaleChartOptions.textClientX - this.leftX < this.showDateRectWith/2){
        if(this.timeScaleChartOptions.clientX - this.leftX < this.showDateRectWith/2){
          this.timeScaleChartOptions.textClientX = this.leftX + this.showDateRectWith/2
        }else {
          this.timeScaleChartOptions.textClientX += this.perStep
        }
        this.update();
      } else if(this.timeScaleChartOptions.textClientX - this.timeScaleChartOptions.clientX < 0){
        this.timeScaleChartOptions.textClientX += this.perStep
        if(this.timeScaleChartOptions.textClientX > this.timeScaleChartOptions.clientX) {
          this.timeScaleChartOptions.textClientX = this.timeScaleChartOptions.clientX
        }
        this.update();
      }
      if(this.video_time_line.textClientX - this.leftX < this.showDateRectWith/2){
        if(this.video_time_line.clientX - this.leftX < this.showDateRectWith/2){
          this.video_time_line.textClientX = this.leftX + this.showDateRectWith/2
        }else {
          this.video_time_line.textClientX += this.perStep
        }
        this.update();
      } else if(this.video_time_line.textClientX - this.video_time_line.clientX < 0){
        this.video_time_line.textClientX += this.perStep
        if(this.video_time_line.textClientX > this.video_time_line.clientX) {
          this.video_time_line.textClientX = this.video_time_line.clientX
        }
        this.update();
      }
    },
    moveRight() {
      if(this.leftX <= 0){
        this.leftX = 0
        return
      }
      this.leftX -= this.perStep
      if(this.timeScaleChartOptions.textClientX - this.leftX > this.showDateRectWith/2){
        if(this.timeScaleChartOptions.textClientX <= this.timeScaleChartOptions.clientX){
          if(this.timeScaleChartOptions.clientX - this.leftX + this.showDateRectWith/2 > 400) {
            this.timeScaleChartOptions.textClientX = 400 + this.leftX - this.showDateRectWith/2
          }else {
            this.timeScaleChartOptions.textClientX = this.timeScaleChartOptions.clientX
          }
        }else {
          this.timeScaleChartOptions.textClientX -= this.perStep
          if(this.timeScaleChartOptions.textClientX < this.timeScaleChartOptions.clientX) {
            this.timeScaleChartOptions.textClientX = this.timeScaleChartOptions.clientX
          }
        }
        this.update();
      }
      if(this.video_time_line.textClientX - this.leftX > this.showDateRectWith/2){
        if(this.video_time_line.textClientX <= this.video_time_line.clientX){
          if(this.video_time_line.clientX - this.leftX + this.showDateRectWith/2 > 400) {
            this.video_time_line.textClientX = 400 + this.leftX - this.showDateRectWith/2
          }else {
            this.video_time_line.textClientX = this.video_time_line.clientX
          }
        }else {
          this.video_time_line.textClientX -= this.perStep
          if(this.video_time_line.textClientX < this.video_time_line.clientX) {
            this.video_time_line.textClientX = this.video_time_line.clientX
          }
        }
        this.update();
      }
    },
    handleClientX(x) {
      if(x - this.leftX < this.showDateRectWith/2) {
        x = this.showDateRectWith/2 + this.leftX
      } else if(x > 400 - this.showDateRectWith/2 + this.leftX) {
        x = 400 - this.showDateRectWith/2 + this.leftX
      }
      return x
    },
    sendTime(time) {
      this.$emit('sendTime', time)
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .timelineBox {
    background: #000000;
    border: 1px solid #303030;
    display: flex;
    height: 48px;
    width: 440px;
  }
  .opacityHalf {
    opacity: 0.5
  }
  .timelineLeft, .timelineRight {
    align-items: center;
    background: #191919;
    border-right: 1px solid #303030;
    display: flex;
    justify-content: center;
    width: 20px;
  }
  .timelineCenterWrap {
    overflow: hidden;
    width: 400px;
    position: relative;
    flex-shrink: 0;
    margin-top: -30px;
  }
  .showDateBox {
    position: absolute;
    width: 400px;
    height: 30px;
    z-index: 10;
  }
</style>
