import Cookies from 'js-cookie'
const navigatorLang = (navigator.language || navigator.browserLanguage).toLowerCase()

const app = {
  state: {
    language: Cookies.get('language') || 'ky', // 默认吉尔吉斯斯坦语
    size: Cookies.get('size') || 'medium'
  },
  mutations: {
    SET_LANGUAGE: (state, language) => {
      state.language = language
      Cookies.set('language', language)
    },
    SET_SIZE: (state, size) => {
      state.size = size
      Cookies.set('size', size)
    }
  },
  actions: {
    setLanguage({ commit }, language) {
      commit('SET_LANGUAGE', language)
    },
    setSize({ commit }, size) {
      commit('SET_SIZE', size)
    }
  }
}

export default app
