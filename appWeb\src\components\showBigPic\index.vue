<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="bigPicDialog"
    @close="hideDialog">
    <section>
      <div class="captionImg">
        <!-- 底图 -->
        <img v-if="big_pic_url !== ''" :src="big_pic_url">
      </div>
    </section>
  </el-dialog>
</template>

<script>
export default {
  name: 'BigPictures',
  components: {

  },
  data() {
    return {
      dialogVisible: false,
      big_pic_url: ''
    }
  },
  methods: {
    show(val) {
      this.big_pic_url = val
      this.dialogVisible = true
    },
    hideDialog() {
      this.dialogVisible = false
      this.big_pic_url = ''
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .bigPicDialog{
    /deep/ .el-dialog{
      width: 800px;
      border-radius: 8px;
    }
    /deep/ .el-dialog__header, /deep/ .el-dialog__body {
      background: #0e2b3d;
    }
    .captionImg{
      margin: 0 auto;
      img{
        width:100%;
      }
    }
  }
</style>

