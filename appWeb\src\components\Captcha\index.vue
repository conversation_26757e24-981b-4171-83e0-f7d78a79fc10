<template>
  <canvas id="captchaCanvas" :width="width" :height="height" @click.stop="generateCaptcha()"></canvas>
</template>

<script>
export default {
  name: 'Captcha',
  props: {
    length: {
      type: Number,
      default: 6
    },
    width: {
      type: Number,
      default: 120
    },
    height: {
      type: Number,
      default: 40
    },
  },
  data () {
    return {
    }
  },
  mounted () {
    // 初始生成验证码
    this.generateCaptcha()
  },
  methods: {
    generateCaptcha() {
        const canvas = document.getElementById('captchaCanvas');
        const ctx = canvas.getContext('2d');
        ctx.clearRect(0, 0, canvas.width, canvas.height); // 清空画布

        const captchaText = this.generateRandomText(this.length); // 生成6位随机字符
        this.$emit('captchaText', captchaText)
        this.drawCaptcha(ctx, canvas, captchaText);
    },
    generateRandomText(length) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    },
    drawCaptcha(ctx, canvas, text) {
        ctx.font = '20px Arial';
        ctx.fillStyle = '#333';
        ctx.textBaseline = 'middle';
        ctx.textAlign = 'center';

        const x = canvas.width / 2;
        const y = canvas.height / 2;

        // 绘制背景
        ctx.fillStyle = '#f0f0f0';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制文本
        ctx.fillStyle = '#333';
        ctx.fillText(text, x, y);

        // 添加干扰线
        for (let i = 0; i < 5; i++) {
            ctx.beginPath();
            ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
            ctx.strokeStyle = '#ccc';
            ctx.stroke();
        }
    }
  }
};
</script>

<style scoped>
.international-icon {
  font-size: 20px;
  cursor: pointer;
  vertical-align: -5px !important;
}
.topBottomArrowIcon {
  width: 18px;
  height: 18px;
  vertical-align: middle;
}
</style>

