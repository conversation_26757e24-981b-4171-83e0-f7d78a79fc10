import { logout, menuList } from '@/api/login'
import { getToken, removeToken, removeRole } from '@/utils/auth'

const user = {
  state: {
    user: '',
    token: getToken(),
    role: '',
    roles: [],
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_ROLE: (state, role) => {
      state.role = role
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
  },

  actions: {
    // 获取菜单信息
    GetMenuList({ commit, state }) {
      return new Promise((resolve, reject) => {
        const data = {
          'time': Date.parse(new Date()) / 1000,
          'account': sessionStorage.username,
          'auth_token': state.token,
        }
        menuList(data).then(response => {
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 登出
    LogOut({ commit, state }, data) {
      return new Promise((resolve, reject) => {
        logout(data).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_ROLE', '')
          sessionStorage.clear()
          removeToken()
          removeRole()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '')
        commit('SET_ROLES', [])
        commit('SET_ROLE', '')
        sessionStorage.clear()
        removeToken()
        removeRole()
        resolve()
      })
    },
  }
}

export default user
